package blackboxcasedatarevision

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"github.com/pkg/errors"
)

type UpdateBlackBoxCaseDataRevisionLogic struct {
	*logic.BaseLogic
}

// update blackbox case data revision
func NewUpdateBlackBoxCaseDataRevisionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateBlackBoxCaseDataRevisionLogic {
	return &UpdateBlackBoxCaseDataRevisionLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *UpdateBlackBoxCaseDataRevisionLogic) UpdateBlackBoxCaseDataRevision(req *types.AppendBlackBoxCaseDataReq) (resp *types.AppendBlackBoxCaseDataResp, err error) {
	in := &pb.AppendBlackBoxCaseDataReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseDataServiceRpc.AppendBlackBoxCaseData(l.Ctx, in)
	if err != nil {
		return nil, err
	}
	resp = &types.AppendBlackBoxCaseDataResp{Items: []*types.BlackBoxCaseData{}, NewItems: []*types.BlackBoxCaseData{}}
	if err = utils.Copy(resp, out, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}
	return resp, nil
}
