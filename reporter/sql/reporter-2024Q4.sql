-- 删除2024年以前的API计划执行记录
DELETE FROM `plan_execution_record` WHERE `cleaned` = 1 AND `created_at` < '2024-01-01';

-- 统计用例7天内执行失败数量
DELETE FROM `case_fail_for_plan_stat` WHERE `created_at` < DATE_SUB(CURDATE(), INTERVAL 10 DAY); -- 删除10天前创建的记录

DROP INDEX `idx_cfs_pipici` ON `case_fail_for_plan_stat`;
CREATE INDEX `ix_cffps_project_id_plan_id_case_id` ON `case_fail_for_plan_stat` (`project_id`, `plan_id`, `case_id`);
CREATE INDEX `ix_cffps_project_id_case_id_case_type` ON `case_fail_for_plan_stat` (`project_id`, `case_id`, `case_type`);
CREATE INDEX `ix_cffps_project_id_case_id_case_type_created_at` ON `case_fail_for_plan_stat` (`project_id`, `case_id`, `case_type`, `created_at`);


-- 压测计划执行记录增加索引
CREATE INDEX `ix_pper_project_id_updated_at` ON `perf_plan_execution_record` (`project_id`, `updated_at`);


-- 压测执行记录保存多个监控地址
UPDATE `perf_plan_execution_record` SET `monitor_url` = NULL WHERE `monitor_url` = '';
UPDATE `perf_plan_execution_record` SET `monitor_url` = JSON_ARRAY(JSON_OBJECT('name', 'cmd & grpc 接口监控', 'type', 1, 'url', `monitor_url`)) WHERE `monitor_url` IS NOT NULL;
ALTER TABLE `perf_plan_execution_record` CHANGE `monitor_url` `monitor_urls` JSON NULL COMMENT '监控面板地址';


-- 常态化压测
ALTER TABLE `perf_plan_execution_record` ADD `protocol` VARCHAR(64) NOT NULL COMMENT '协议' AFTER `target_duration`;
ALTER TABLE `perf_plan_execution_record` ADD `services` JSON NULL COMMENT '计划涉及的服务的元数据' AFTER `execution_mode`;

ALTER TABLE `perf_case_execution_record` ADD `steps` JSON NULL COMMENT '用例步骤' AFTER `case_name`;
ALTER TABLE `perf_case_execution_record` ADD `perf_data` JSON NULL COMMENT '压测数据' AFTER `steps`;
ALTER TABLE `perf_case_execution_record` ADD `load_generator` JSON NULL COMMENT '施压机资源' AFTER `perf_data`;
