// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package uiagentreporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type (
	CaseFailForPlanStatForMq                                      = pb.CaseFailForPlanStatForMq
	CleanConfig                                                   = pb.CleanConfig
	CleanConfigs                                                  = pb.CleanConfigs
	CountFailedCaseInLastNDaysReq                                 = pb.CountFailedCaseInLastNDaysReq
	CountFailedCaseInLastNDaysResp                                = pb.CountFailedCaseInLastNDaysResp
	CreateInterfaceRecordResponse                                 = pb.CreateInterfaceRecordResponse
	CreatePerfCaseRecordReq                                       = pb.CreatePerfCaseRecordReq
	CreatePerfCaseRecordResp                                      = pb.CreatePerfCaseRecordResp
	CreatePerfPlanRecordReq                                       = pb.CreatePerfPlanRecordReq
	CreatePerfPlanRecordResp                                      = pb.CreatePerfPlanRecordResp
	CreatePerfSuiteRecordReq                                      = pb.CreatePerfSuiteRecordReq
	CreatePerfSuiteRecordResp                                     = pb.CreatePerfSuiteRecordResp
	CreatePlanRecordResponse                                      = pb.CreatePlanRecordResponse
	CreateRecordResponse                                          = pb.CreateRecordResponse
	CreateServiceRecordResponse                                   = pb.CreateServiceRecordResponse
	CreateStabilityDeviceRecordResp                               = pb.CreateStabilityDeviceRecordResp
	CreateStabilityPlanRecordResp                                 = pb.CreateStabilityPlanRecordResp
	CreateSuiteRecordResponse                                     = pb.CreateSuiteRecordResponse
	CreateUIAgentComponentRecordReq                               = pb.CreateUIAgentComponentRecordReq
	CreateUIAgentComponentRecordResp                              = pb.CreateUIAgentComponentRecordResp
	CreateUICaseRecordResponse                                    = pb.CreateUICaseRecordResponse
	CreateUIPlanRecordResponse                                    = pb.CreateUIPlanRecordResponse
	CreateUISuiteRecordResponse                                   = pb.CreateUISuiteRecordResponse
	DelCaseFailStatForPlanReq                                     = pb.DelCaseFailStatForPlanReq
	DelCaseFailStatForPlanResp                                    = pb.DelCaseFailStatForPlanResp
	FindRedundantPlanRecordResp                                   = pb.FindRedundantPlanRecordResp
	GetCaseLatestRecordRequest                                    = pb.GetCaseLatestRecordRequest
	GetCaseLatestRecordResponse                                   = pb.GetCaseLatestRecordResponse
	GetCaseLatestRecordResponse_RecordCaseRecord                  = pb.GetCaseLatestRecordResponse_RecordCaseRecord
	GetChildrenRecordRequest                                      = pb.GetChildrenRecordRequest
	GetChildrenRecordResponse                                     = pb.GetChildrenRecordResponse
	GetChildrenRecordResponse_ChildRecord                         = pb.GetChildrenRecordResponse_ChildRecord
	GetCountOfUIAgentComponentRecordsByImageReq                   = pb.GetCountOfUIAgentComponentRecordsByImageReq
	GetCountOfUIAgentComponentRecordsByImageResp                  = pb.GetCountOfUIAgentComponentRecordsByImageResp
	GetExecuteRecordRequest                                       = pb.GetExecuteRecordRequest
	GetExecuteRecordResponse                                      = pb.GetExecuteRecordResponse
	GetInterfaceRecordRequest                                     = pb.GetInterfaceRecordRequest
	GetInterfaceRecordResponse                                    = pb.GetInterfaceRecordResponse
	GetInterfaceRecordResponse_CaseItem                           = pb.GetInterfaceRecordResponse_CaseItem
	GetParentRecordRequest                                        = pb.GetParentRecordRequest
	GetParentRecordResponse                                       = pb.GetParentRecordResponse
	GetPerfCaseRecordReq                                          = pb.GetPerfCaseRecordReq
	GetPerfCaseRecordResp                                         = pb.GetPerfCaseRecordResp
	GetPerfPlanRecordReq                                          = pb.GetPerfPlanRecordReq
	GetPerfPlanRecordResp                                         = pb.GetPerfPlanRecordResp
	GetPerfSuiteRecordReq                                         = pb.GetPerfSuiteRecordReq
	GetPerfSuiteRecordResp                                        = pb.GetPerfSuiteRecordResp
	GetPlanCasesInfoRequest                                       = pb.GetPlanCasesInfoRequest
	GetPlanCasesInfoResponse                                      = pb.GetPlanCasesInfoResponse
	GetPlanRecordRequest                                          = pb.GetPlanRecordRequest
	GetPlanRecordResponse                                         = pb.GetPlanRecordResponse
	GetPlanRecordResponse_InterfaceDocumentItem                   = pb.GetPlanRecordResponse_InterfaceDocumentItem
	GetPlanRecordResponse_ServiceItem                             = pb.GetPlanRecordResponse_ServiceItem
	GetPlanRecordResponse_SuiteItem                               = pb.GetPlanRecordResponse_SuiteItem
	GetPlanSummaryRequest                                         = pb.GetPlanSummaryRequest
	GetPlanSummaryResponse                                        = pb.GetPlanSummaryResponse
	GetPlanSummaryResponse_Record                                 = pb.GetPlanSummaryResponse_Record
	GetPlanTimeScaleRequest                                       = pb.GetPlanTimeScaleRequest
	GetPlanTimeScaleResponse                                      = pb.GetPlanTimeScaleResponse
	GetPlanTimeScaleResponse_CaseRecord                           = pb.GetPlanTimeScaleResponse_CaseRecord
	GetPlanTimeScaleResponse_SuiteRecord                          = pb.GetPlanTimeScaleResponse_SuiteRecord
	GetServiceRecordRequest                                       = pb.GetServiceRecordRequest
	GetServiceRecordResponse                                      = pb.GetServiceRecordResponse
	GetServiceRecordResponse_CaseItem                             = pb.GetServiceRecordResponse_CaseItem
	GetStabilityDeviceActivityReq                                 = pb.GetStabilityDeviceActivityReq
	GetStabilityDeviceActivityResp                                = pb.GetStabilityDeviceActivityResp
	GetStabilityDevicePerfDataReq                                 = pb.GetStabilityDevicePerfDataReq
	GetStabilityDevicePerfDataResp                                = pb.GetStabilityDevicePerfDataResp
	GetStabilityPlanRecordReq                                     = pb.GetStabilityPlanRecordReq
	GetStabilityPlanRecordResp                                    = pb.GetStabilityPlanRecordResp
	GetSuiteRecordRequest                                         = pb.GetSuiteRecordRequest
	GetSuiteRecordResponse                                        = pb.GetSuiteRecordResponse
	GetSuiteRecordResponse_CaseItem                               = pb.GetSuiteRecordResponse_CaseItem
	GetUIAgentComponentRecordReq                                  = pb.GetUIAgentComponentRecordReq
	GetUIAgentComponentRecordResp                                 = pb.GetUIAgentComponentRecordResp
	GetUIAgentComponentRecordsByUDIDReq                           = pb.GetUIAgentComponentRecordsByUDIDReq
	GetUIAgentComponentRecordsByUDIDResp                          = pb.GetUIAgentComponentRecordsByUDIDResp
	GetUICaseRecordReq                                            = pb.GetUICaseRecordReq
	GetUICaseRecordResp                                           = pb.GetUICaseRecordResp
	GetUICaseStepReq                                              = pb.GetUICaseStepReq
	GetUICaseStepResp                                             = pb.GetUICaseStepResp
	GetUIDevicePerfDataReq                                        = pb.GetUIDevicePerfDataReq
	GetUIDevicePerfDataResp                                       = pb.GetUIDevicePerfDataResp
	GetUIPlanCasesInfoRequest                                     = pb.GetUIPlanCasesInfoRequest
	GetUIPlanCasesInfoResponse                                    = pb.GetUIPlanCasesInfoResponse
	GetUIPlanRecordReq                                            = pb.GetUIPlanRecordReq
	GetUIPlanRecordResp                                           = pb.GetUIPlanRecordResp
	HandleUIAgentRecordTaskInfo                                   = pb.HandleUIAgentRecordTaskInfo
	ListFailCaseRecordForPlanRequest                              = pb.ListFailCaseRecordForPlanRequest
	ListFailCaseRecordForPlanResponse                             = pb.ListFailCaseRecordForPlanResponse
	ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord = pb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord
	ListInterfaceRecordRequest                                    = pb.ListInterfaceRecordRequest
	ListInterfaceRecordResponse                                   = pb.ListInterfaceRecordResponse
	ListInterfaceRecordResponse_InterfaceRecord                   = pb.ListInterfaceRecordResponse_InterfaceRecord
	ListPlanRecordRequest                                         = pb.ListPlanRecordRequest
	ListPlanRecordResponse                                        = pb.ListPlanRecordResponse
	ListPlanRecordResponse_PlanRecord                             = pb.ListPlanRecordResponse_PlanRecord
	ListServiceRecordRequest                                      = pb.ListServiceRecordRequest
	ListServiceRecordResponse                                     = pb.ListServiceRecordResponse
	ListServiceRecordResponse_ServiceRecord                       = pb.ListServiceRecordResponse_ServiceRecord
	ListStabilityDeviceStepReq                                    = pb.ListStabilityDeviceStepReq
	ListStabilityDeviceStepResp                                   = pb.ListStabilityDeviceStepResp
	ListStabilityPlanRecordReq                                    = pb.ListStabilityPlanRecordReq
	ListStabilityPlanRecordResp                                   = pb.ListStabilityPlanRecordResp
	ListSuiteRecordRequest                                        = pb.ListSuiteRecordRequest
	ListSuiteRecordResponse                                       = pb.ListSuiteRecordResponse
	ListSuiteRecordResponse_SuiteRecord                           = pb.ListSuiteRecordResponse_SuiteRecord
	ListUICaseStepReq                                             = pb.ListUICaseStepReq
	ListUICaseStepResp                                            = pb.ListUICaseStepResp
	ListUIPlanRecordRequest                                       = pb.ListUIPlanRecordRequest
	ListUIPlanRecordResponse                                      = pb.ListUIPlanRecordResponse
	ListUIPlanRecordResponse_PlanRecord                           = pb.ListUIPlanRecordResponse_PlanRecord
	ModifyInterfaceRecordResponse                                 = pb.ModifyInterfaceRecordResponse
	ModifyPerfCaseRecordReq                                       = pb.ModifyPerfCaseRecordReq
	ModifyPerfCaseRecordResp                                      = pb.ModifyPerfCaseRecordResp
	ModifyPerfPlanRecordReq                                       = pb.ModifyPerfPlanRecordReq
	ModifyPerfPlanRecordResp                                      = pb.ModifyPerfPlanRecordResp
	ModifyPerfSuiteRecordReq                                      = pb.ModifyPerfSuiteRecordReq
	ModifyPerfSuiteRecordResp                                     = pb.ModifyPerfSuiteRecordResp
	ModifyPlanRecordResponse                                      = pb.ModifyPlanRecordResponse
	ModifyRecordResponse                                          = pb.ModifyRecordResponse
	ModifyServiceRecordResponse                                   = pb.ModifyServiceRecordResponse
	ModifyStabilityDeviceRecordResp                               = pb.ModifyStabilityDeviceRecordResp
	ModifyStabilityPlanRecordResp                                 = pb.ModifyStabilityPlanRecordResp
	ModifySuiteRecordResponse                                     = pb.ModifySuiteRecordResponse
	ModifyUIAgentComponentRecordReq                               = pb.ModifyUIAgentComponentRecordReq
	ModifyUIAgentComponentRecordResp                              = pb.ModifyUIAgentComponentRecordResp
	ModifyUICaseRecordResponse                                    = pb.ModifyUICaseRecordResponse
	ModifyUIPlanRecordResponse                                    = pb.ModifyUIPlanRecordResponse
	ModifyUISuiteRecordResponse                                   = pb.ModifyUISuiteRecordResponse
	PutInterfaceRecordRequest                                     = pb.PutInterfaceRecordRequest
	PutPlanRecordRequest                                          = pb.PutPlanRecordRequest
	PutRecordRequest                                              = pb.PutRecordRequest
	PutServiceRecordRequest                                       = pb.PutServiceRecordRequest
	PutStabilityDeviceRecordReq                                   = pb.PutStabilityDeviceRecordReq
	PutStabilityPlanRecordReq                                     = pb.PutStabilityPlanRecordReq
	PutSuiteRecordRequest                                         = pb.PutSuiteRecordRequest
	PutUICaseRecordRequest                                        = pb.PutUICaseRecordRequest
	PutUIPlanRecordRequest                                        = pb.PutUIPlanRecordRequest
	PutUISuiteRecordRequest                                       = pb.PutUISuiteRecordRequest
	RedundantPlanRecord                                           = pb.RedundantPlanRecord
	SaveUIDevicePerfDataReq                                       = pb.SaveUIDevicePerfDataReq
	SaveUIDevicePerfDataResp                                      = pb.SaveUIDevicePerfDataResp
	SearchPerfCaseRecordReq                                       = pb.SearchPerfCaseRecordReq
	SearchPerfCaseRecordResp                                      = pb.SearchPerfCaseRecordResp
	SearchPerfPlanRecordReq                                       = pb.SearchPerfPlanRecordReq
	SearchPerfPlanRecordResp                                      = pb.SearchPerfPlanRecordResp
	SearchStabilityDeviceRecordReq                                = pb.SearchStabilityDeviceRecordReq
	SearchStabilityDeviceRecordResp                               = pb.SearchStabilityDeviceRecordResp
	SearchStabilityPlanRecordReq                                  = pb.SearchStabilityPlanRecordReq
	SearchStabilityPlanRecordResp                                 = pb.SearchStabilityPlanRecordResp
	SearchUIAgentComponentRecordReq                               = pb.SearchUIAgentComponentRecordReq
	SearchUIAgentComponentRecordResp                              = pb.SearchUIAgentComponentRecordResp
	SearchUICaseRecordReq                                         = pb.SearchUICaseRecordReq
	SearchUICaseRecordResp                                        = pb.SearchUICaseRecordResp
	SearchUIDeviceRecordReq                                       = pb.SearchUIDeviceRecordReq
	SearchUIDeviceRecordResp                                      = pb.SearchUIDeviceRecordResp
	SearchUISuiteRecordReq                                        = pb.SearchUISuiteRecordReq
	SearchUISuiteRecordResp                                       = pb.SearchUISuiteRecordResp
	UpdateMonitorURLOfPerfPlanRecordReq                           = pb.UpdateMonitorURLOfPerfPlanRecordReq
	UpdateMonitorURLOfPerfPlanRecordResp                          = pb.UpdateMonitorURLOfPerfPlanRecordResp
	ViewUIPlanRecordRequest                                       = pb.ViewUIPlanRecordRequest
	ViewUIPlanRecordResponse                                      = pb.ViewUIPlanRecordResponse

	UIAgentReporter interface {
		// CreateUIAgentComponentRecord 创建`UI Agent`组件执行记录
		CreateUIAgentComponentRecord(ctx context.Context, in *CreateUIAgentComponentRecordReq, opts ...grpc.CallOption) (*CreateUIAgentComponentRecordResp, error)
		// ModifyUIAgentComponentRecord 修改`UI Agent`组件执行记录
		ModifyUIAgentComponentRecord(ctx context.Context, in *ModifyUIAgentComponentRecordReq, opts ...grpc.CallOption) (*ModifyUIAgentComponentRecordResp, error)
		// SearchUIAgentComponentRecord 搜索`UI Agent`组件执行记录
		SearchUIAgentComponentRecord(ctx context.Context, in *SearchUIAgentComponentRecordReq, opts ...grpc.CallOption) (*SearchUIAgentComponentRecordResp, error)
		// GetUIAgentComponentRecord 获取`UI Agent`组件执行记录
		GetUIAgentComponentRecord(ctx context.Context, in *GetUIAgentComponentRecordReq, opts ...grpc.CallOption) (*GetUIAgentComponentRecordResp, error)
		// GetCountOfUIAgentComponentRecordsByImage 获取涉及指定图片的`UI Agent`组件执行记录数量
		GetCountOfUIAgentComponentRecordsByImage(ctx context.Context, in *GetCountOfUIAgentComponentRecordsByImageReq, opts ...grpc.CallOption) (*GetCountOfUIAgentComponentRecordsByImageResp, error)
		// GetUIAgentComponentRecordsByUDID 获取使用指定设备的`UI Agent`组件执行记录
		GetUIAgentComponentRecordsByUDID(ctx context.Context, in *GetUIAgentComponentRecordsByUDIDReq, opts ...grpc.CallOption) (*GetUIAgentComponentRecordsByUDIDResp, error)
	}

	defaultUIAgentReporter struct {
		cli zrpc.Client
	}
)

func NewUIAgentReporter(cli zrpc.Client) UIAgentReporter {
	return &defaultUIAgentReporter{
		cli: cli,
	}
}

// CreateUIAgentComponentRecord 创建`UI Agent`组件执行记录
func (m *defaultUIAgentReporter) CreateUIAgentComponentRecord(ctx context.Context, in *CreateUIAgentComponentRecordReq, opts ...grpc.CallOption) (*CreateUIAgentComponentRecordResp, error) {
	client := pb.NewUIAgentReporterClient(m.cli.Conn())
	return client.CreateUIAgentComponentRecord(ctx, in, opts...)
}

// ModifyUIAgentComponentRecord 修改`UI Agent`组件执行记录
func (m *defaultUIAgentReporter) ModifyUIAgentComponentRecord(ctx context.Context, in *ModifyUIAgentComponentRecordReq, opts ...grpc.CallOption) (*ModifyUIAgentComponentRecordResp, error) {
	client := pb.NewUIAgentReporterClient(m.cli.Conn())
	return client.ModifyUIAgentComponentRecord(ctx, in, opts...)
}

// SearchUIAgentComponentRecord 搜索`UI Agent`组件执行记录
func (m *defaultUIAgentReporter) SearchUIAgentComponentRecord(ctx context.Context, in *SearchUIAgentComponentRecordReq, opts ...grpc.CallOption) (*SearchUIAgentComponentRecordResp, error) {
	client := pb.NewUIAgentReporterClient(m.cli.Conn())
	return client.SearchUIAgentComponentRecord(ctx, in, opts...)
}

// GetUIAgentComponentRecord 获取`UI Agent`组件执行记录
func (m *defaultUIAgentReporter) GetUIAgentComponentRecord(ctx context.Context, in *GetUIAgentComponentRecordReq, opts ...grpc.CallOption) (*GetUIAgentComponentRecordResp, error) {
	client := pb.NewUIAgentReporterClient(m.cli.Conn())
	return client.GetUIAgentComponentRecord(ctx, in, opts...)
}

// GetCountOfUIAgentComponentRecordsByImage 获取涉及指定图片的`UI Agent`组件执行记录数量
func (m *defaultUIAgentReporter) GetCountOfUIAgentComponentRecordsByImage(ctx context.Context, in *GetCountOfUIAgentComponentRecordsByImageReq, opts ...grpc.CallOption) (*GetCountOfUIAgentComponentRecordsByImageResp, error) {
	client := pb.NewUIAgentReporterClient(m.cli.Conn())
	return client.GetCountOfUIAgentComponentRecordsByImage(ctx, in, opts...)
}

// GetUIAgentComponentRecordsByUDID 获取使用指定设备的`UI Agent`组件执行记录
func (m *defaultUIAgentReporter) GetUIAgentComponentRecordsByUDID(ctx context.Context, in *GetUIAgentComponentRecordsByUDIDReq, opts ...grpc.CallOption) (*GetUIAgentComponentRecordsByUDIDResp, error) {
	client := pb.NewUIAgentReporterClient(m.cli.Conn())
	return client.GetUIAgentComponentRecordsByUDID(ctx, in, opts...)
}
