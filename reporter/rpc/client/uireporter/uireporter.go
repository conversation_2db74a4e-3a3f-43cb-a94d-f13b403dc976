// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package uireporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type (
	CaseFailForPlanStatForMq                                      = pb.CaseFailForPlanStatForMq
	CleanConfig                                                   = pb.CleanConfig
	CleanConfigs                                                  = pb.CleanConfigs
	CountFailedCaseInLastNDaysReq                                 = pb.CountFailedCaseInLastNDaysReq
	CountFailedCaseInLastNDaysResp                                = pb.CountFailedCaseInLastNDaysResp
	CreateInterfaceRecordResponse                                 = pb.CreateInterfaceRecordResponse
	CreatePerfCaseRecordReq                                       = pb.CreatePerfCaseRecordReq
	CreatePerfCaseRecordResp                                      = pb.CreatePerfCaseRecordResp
	CreatePerfPlanRecordReq                                       = pb.CreatePerfPlanRecordReq
	CreatePerfPlanRecordResp                                      = pb.CreatePerfPlanRecordResp
	CreatePerfSuiteRecordReq                                      = pb.CreatePerfSuiteRecordReq
	CreatePerfSuiteRecordResp                                     = pb.CreatePerfSuiteRecordResp
	CreatePlanRecordResponse                                      = pb.CreatePlanRecordResponse
	CreateRecordResponse                                          = pb.CreateRecordResponse
	CreateServiceRecordResponse                                   = pb.CreateServiceRecordResponse
	CreateStabilityDeviceRecordResp                               = pb.CreateStabilityDeviceRecordResp
	CreateStabilityPlanRecordResp                                 = pb.CreateStabilityPlanRecordResp
	CreateSuiteRecordResponse                                     = pb.CreateSuiteRecordResponse
	CreateUIAgentComponentRecordReq                               = pb.CreateUIAgentComponentRecordReq
	CreateUIAgentComponentRecordResp                              = pb.CreateUIAgentComponentRecordResp
	CreateUICaseRecordResponse                                    = pb.CreateUICaseRecordResponse
	CreateUIPlanRecordResponse                                    = pb.CreateUIPlanRecordResponse
	CreateUISuiteRecordResponse                                   = pb.CreateUISuiteRecordResponse
	DelCaseFailStatForPlanReq                                     = pb.DelCaseFailStatForPlanReq
	DelCaseFailStatForPlanResp                                    = pb.DelCaseFailStatForPlanResp
	FindRedundantPlanRecordResp                                   = pb.FindRedundantPlanRecordResp
	GetCaseLatestRecordRequest                                    = pb.GetCaseLatestRecordRequest
	GetCaseLatestRecordResponse                                   = pb.GetCaseLatestRecordResponse
	GetCaseLatestRecordResponse_RecordCaseRecord                  = pb.GetCaseLatestRecordResponse_RecordCaseRecord
	GetChildrenRecordRequest                                      = pb.GetChildrenRecordRequest
	GetChildrenRecordResponse                                     = pb.GetChildrenRecordResponse
	GetChildrenRecordResponse_ChildRecord                         = pb.GetChildrenRecordResponse_ChildRecord
	GetCountOfUIAgentComponentRecordsByImageReq                   = pb.GetCountOfUIAgentComponentRecordsByImageReq
	GetCountOfUIAgentComponentRecordsByImageResp                  = pb.GetCountOfUIAgentComponentRecordsByImageResp
	GetExecuteRecordRequest                                       = pb.GetExecuteRecordRequest
	GetExecuteRecordResponse                                      = pb.GetExecuteRecordResponse
	GetInterfaceRecordRequest                                     = pb.GetInterfaceRecordRequest
	GetInterfaceRecordResponse                                    = pb.GetInterfaceRecordResponse
	GetInterfaceRecordResponse_CaseItem                           = pb.GetInterfaceRecordResponse_CaseItem
	GetParentRecordRequest                                        = pb.GetParentRecordRequest
	GetParentRecordResponse                                       = pb.GetParentRecordResponse
	GetPerfCaseRecordReq                                          = pb.GetPerfCaseRecordReq
	GetPerfCaseRecordResp                                         = pb.GetPerfCaseRecordResp
	GetPerfPlanRecordReq                                          = pb.GetPerfPlanRecordReq
	GetPerfPlanRecordResp                                         = pb.GetPerfPlanRecordResp
	GetPerfSuiteRecordReq                                         = pb.GetPerfSuiteRecordReq
	GetPerfSuiteRecordResp                                        = pb.GetPerfSuiteRecordResp
	GetPlanCasesInfoRequest                                       = pb.GetPlanCasesInfoRequest
	GetPlanCasesInfoResponse                                      = pb.GetPlanCasesInfoResponse
	GetPlanRecordRequest                                          = pb.GetPlanRecordRequest
	GetPlanRecordResponse                                         = pb.GetPlanRecordResponse
	GetPlanRecordResponse_InterfaceDocumentItem                   = pb.GetPlanRecordResponse_InterfaceDocumentItem
	GetPlanRecordResponse_ServiceItem                             = pb.GetPlanRecordResponse_ServiceItem
	GetPlanRecordResponse_SuiteItem                               = pb.GetPlanRecordResponse_SuiteItem
	GetPlanSummaryRequest                                         = pb.GetPlanSummaryRequest
	GetPlanSummaryResponse                                        = pb.GetPlanSummaryResponse
	GetPlanSummaryResponse_Record                                 = pb.GetPlanSummaryResponse_Record
	GetPlanTimeScaleRequest                                       = pb.GetPlanTimeScaleRequest
	GetPlanTimeScaleResponse                                      = pb.GetPlanTimeScaleResponse
	GetPlanTimeScaleResponse_CaseRecord                           = pb.GetPlanTimeScaleResponse_CaseRecord
	GetPlanTimeScaleResponse_SuiteRecord                          = pb.GetPlanTimeScaleResponse_SuiteRecord
	GetServiceRecordRequest                                       = pb.GetServiceRecordRequest
	GetServiceRecordResponse                                      = pb.GetServiceRecordResponse
	GetServiceRecordResponse_CaseItem                             = pb.GetServiceRecordResponse_CaseItem
	GetStabilityDeviceActivityReq                                 = pb.GetStabilityDeviceActivityReq
	GetStabilityDeviceActivityResp                                = pb.GetStabilityDeviceActivityResp
	GetStabilityDevicePerfDataReq                                 = pb.GetStabilityDevicePerfDataReq
	GetStabilityDevicePerfDataResp                                = pb.GetStabilityDevicePerfDataResp
	GetStabilityPlanRecordReq                                     = pb.GetStabilityPlanRecordReq
	GetStabilityPlanRecordResp                                    = pb.GetStabilityPlanRecordResp
	GetSuiteRecordRequest                                         = pb.GetSuiteRecordRequest
	GetSuiteRecordResponse                                        = pb.GetSuiteRecordResponse
	GetSuiteRecordResponse_CaseItem                               = pb.GetSuiteRecordResponse_CaseItem
	GetUIAgentComponentRecordReq                                  = pb.GetUIAgentComponentRecordReq
	GetUIAgentComponentRecordResp                                 = pb.GetUIAgentComponentRecordResp
	GetUIAgentComponentRecordsByUDIDReq                           = pb.GetUIAgentComponentRecordsByUDIDReq
	GetUIAgentComponentRecordsByUDIDResp                          = pb.GetUIAgentComponentRecordsByUDIDResp
	GetUICaseRecordReq                                            = pb.GetUICaseRecordReq
	GetUICaseRecordResp                                           = pb.GetUICaseRecordResp
	GetUICaseStepReq                                              = pb.GetUICaseStepReq
	GetUICaseStepResp                                             = pb.GetUICaseStepResp
	GetUIDevicePerfDataReq                                        = pb.GetUIDevicePerfDataReq
	GetUIDevicePerfDataResp                                       = pb.GetUIDevicePerfDataResp
	GetUIPlanCasesInfoRequest                                     = pb.GetUIPlanCasesInfoRequest
	GetUIPlanCasesInfoResponse                                    = pb.GetUIPlanCasesInfoResponse
	GetUIPlanRecordReq                                            = pb.GetUIPlanRecordReq
	GetUIPlanRecordResp                                           = pb.GetUIPlanRecordResp
	HandleUIAgentRecordTaskInfo                                   = pb.HandleUIAgentRecordTaskInfo
	ListFailCaseRecordForPlanRequest                              = pb.ListFailCaseRecordForPlanRequest
	ListFailCaseRecordForPlanResponse                             = pb.ListFailCaseRecordForPlanResponse
	ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord = pb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord
	ListInterfaceRecordRequest                                    = pb.ListInterfaceRecordRequest
	ListInterfaceRecordResponse                                   = pb.ListInterfaceRecordResponse
	ListInterfaceRecordResponse_InterfaceRecord                   = pb.ListInterfaceRecordResponse_InterfaceRecord
	ListPlanRecordRequest                                         = pb.ListPlanRecordRequest
	ListPlanRecordResponse                                        = pb.ListPlanRecordResponse
	ListPlanRecordResponse_PlanRecord                             = pb.ListPlanRecordResponse_PlanRecord
	ListServiceRecordRequest                                      = pb.ListServiceRecordRequest
	ListServiceRecordResponse                                     = pb.ListServiceRecordResponse
	ListServiceRecordResponse_ServiceRecord                       = pb.ListServiceRecordResponse_ServiceRecord
	ListStabilityDeviceStepReq                                    = pb.ListStabilityDeviceStepReq
	ListStabilityDeviceStepResp                                   = pb.ListStabilityDeviceStepResp
	ListStabilityPlanRecordReq                                    = pb.ListStabilityPlanRecordReq
	ListStabilityPlanRecordResp                                   = pb.ListStabilityPlanRecordResp
	ListSuiteRecordRequest                                        = pb.ListSuiteRecordRequest
	ListSuiteRecordResponse                                       = pb.ListSuiteRecordResponse
	ListSuiteRecordResponse_SuiteRecord                           = pb.ListSuiteRecordResponse_SuiteRecord
	ListUICaseStepReq                                             = pb.ListUICaseStepReq
	ListUICaseStepResp                                            = pb.ListUICaseStepResp
	ListUIPlanRecordRequest                                       = pb.ListUIPlanRecordRequest
	ListUIPlanRecordResponse                                      = pb.ListUIPlanRecordResponse
	ListUIPlanRecordResponse_PlanRecord                           = pb.ListUIPlanRecordResponse_PlanRecord
	ModifyInterfaceRecordResponse                                 = pb.ModifyInterfaceRecordResponse
	ModifyPerfCaseRecordReq                                       = pb.ModifyPerfCaseRecordReq
	ModifyPerfCaseRecordResp                                      = pb.ModifyPerfCaseRecordResp
	ModifyPerfPlanRecordReq                                       = pb.ModifyPerfPlanRecordReq
	ModifyPerfPlanRecordResp                                      = pb.ModifyPerfPlanRecordResp
	ModifyPerfSuiteRecordReq                                      = pb.ModifyPerfSuiteRecordReq
	ModifyPerfSuiteRecordResp                                     = pb.ModifyPerfSuiteRecordResp
	ModifyPlanRecordResponse                                      = pb.ModifyPlanRecordResponse
	ModifyRecordResponse                                          = pb.ModifyRecordResponse
	ModifyServiceRecordResponse                                   = pb.ModifyServiceRecordResponse
	ModifyStabilityDeviceRecordResp                               = pb.ModifyStabilityDeviceRecordResp
	ModifyStabilityPlanRecordResp                                 = pb.ModifyStabilityPlanRecordResp
	ModifySuiteRecordResponse                                     = pb.ModifySuiteRecordResponse
	ModifyUIAgentComponentRecordReq                               = pb.ModifyUIAgentComponentRecordReq
	ModifyUIAgentComponentRecordResp                              = pb.ModifyUIAgentComponentRecordResp
	ModifyUICaseRecordResponse                                    = pb.ModifyUICaseRecordResponse
	ModifyUIPlanRecordResponse                                    = pb.ModifyUIPlanRecordResponse
	ModifyUISuiteRecordResponse                                   = pb.ModifyUISuiteRecordResponse
	PutInterfaceRecordRequest                                     = pb.PutInterfaceRecordRequest
	PutPlanRecordRequest                                          = pb.PutPlanRecordRequest
	PutRecordRequest                                              = pb.PutRecordRequest
	PutServiceRecordRequest                                       = pb.PutServiceRecordRequest
	PutStabilityDeviceRecordReq                                   = pb.PutStabilityDeviceRecordReq
	PutStabilityPlanRecordReq                                     = pb.PutStabilityPlanRecordReq
	PutSuiteRecordRequest                                         = pb.PutSuiteRecordRequest
	PutUICaseRecordRequest                                        = pb.PutUICaseRecordRequest
	PutUIPlanRecordRequest                                        = pb.PutUIPlanRecordRequest
	PutUISuiteRecordRequest                                       = pb.PutUISuiteRecordRequest
	RedundantPlanRecord                                           = pb.RedundantPlanRecord
	SaveUIDevicePerfDataReq                                       = pb.SaveUIDevicePerfDataReq
	SaveUIDevicePerfDataResp                                      = pb.SaveUIDevicePerfDataResp
	SearchPerfCaseRecordReq                                       = pb.SearchPerfCaseRecordReq
	SearchPerfCaseRecordResp                                      = pb.SearchPerfCaseRecordResp
	SearchPerfPlanRecordReq                                       = pb.SearchPerfPlanRecordReq
	SearchPerfPlanRecordResp                                      = pb.SearchPerfPlanRecordResp
	SearchStabilityDeviceRecordReq                                = pb.SearchStabilityDeviceRecordReq
	SearchStabilityDeviceRecordResp                               = pb.SearchStabilityDeviceRecordResp
	SearchStabilityPlanRecordReq                                  = pb.SearchStabilityPlanRecordReq
	SearchStabilityPlanRecordResp                                 = pb.SearchStabilityPlanRecordResp
	SearchUIAgentComponentRecordReq                               = pb.SearchUIAgentComponentRecordReq
	SearchUIAgentComponentRecordResp                              = pb.SearchUIAgentComponentRecordResp
	SearchUICaseRecordReq                                         = pb.SearchUICaseRecordReq
	SearchUICaseRecordResp                                        = pb.SearchUICaseRecordResp
	SearchUIDeviceRecordReq                                       = pb.SearchUIDeviceRecordReq
	SearchUIDeviceRecordResp                                      = pb.SearchUIDeviceRecordResp
	SearchUISuiteRecordReq                                        = pb.SearchUISuiteRecordReq
	SearchUISuiteRecordResp                                       = pb.SearchUISuiteRecordResp
	UpdateMonitorURLOfPerfPlanRecordReq                           = pb.UpdateMonitorURLOfPerfPlanRecordReq
	UpdateMonitorURLOfPerfPlanRecordResp                          = pb.UpdateMonitorURLOfPerfPlanRecordResp
	ViewUIPlanRecordRequest                                       = pb.ViewUIPlanRecordRequest
	ViewUIPlanRecordResponse                                      = pb.ViewUIPlanRecordResponse

	UIReporter interface {
		// CreateUICaseRecord 创建UI用例执行记录
		CreateUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*CreateUICaseRecordResponse, error)
		// ModifyUICaseRecord 修改UI用例执行记录
		ModifyUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*ModifyUICaseRecordResponse, error)
		// CreateUISuiteRecord 创建UI集合执行记录
		CreateUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*CreateUISuiteRecordResponse, error)
		// ModifyUISuiteRecord 修改UI集合执行记录
		ModifyUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*ModifyUISuiteRecordResponse, error)
		// CreateUIPlanRecord 创建UI计划执行记录
		CreateUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*CreateUIPlanRecordResponse, error)
		// ModifyUIPlanRecord 修改UI计划执行记录
		ModifyUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*ModifyUIPlanRecordResponse, error)
		// ViewUIPlanRecord 查看UI计划执行记录
		ViewUIPlanRecord(ctx context.Context, in *ViewUIPlanRecordRequest, opts ...grpc.CallOption) (*ViewUIPlanRecordResponse, error)
		// ListUIPlanRecord UI计划执行记录列表
		ListUIPlanRecord(ctx context.Context, in *ListUIPlanRecordRequest, opts ...grpc.CallOption) (*ListUIPlanRecordResponse, error)
		// GetUIPlanCasesInfo 获取UI计划关联用例信息
		GetUIPlanCasesInfo(ctx context.Context, in *GetUIPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetUIPlanCasesInfoResponse, error)
		// GetUIPlanRecord 获取UI计划执行记录
		GetUIPlanRecord(ctx context.Context, in *GetUIPlanRecordReq, opts ...grpc.CallOption) (*GetUIPlanRecordResp, error)
		// SearchUISuiteRecord 搜索UI计划执行记录下的UI集合执行记录
		SearchUISuiteRecord(ctx context.Context, in *SearchUISuiteRecordReq, opts ...grpc.CallOption) (*SearchUISuiteRecordResp, error)
		// SearchUICaseRecord 搜索UI集合执行记录下的UI用例执行记录
		SearchUICaseRecord(ctx context.Context, in *SearchUICaseRecordReq, opts ...grpc.CallOption) (*SearchUICaseRecordResp, error)
		// GetUICaseRecord 获取UI用例执行记录
		GetUICaseRecord(ctx context.Context, in *GetUICaseRecordReq, opts ...grpc.CallOption) (*GetUICaseRecordResp, error)
		// ListUICaseStep 获取UI用例执行步骤列表
		ListUICaseStep(ctx context.Context, in *ListUICaseStepReq, opts ...grpc.CallOption) (*ListUICaseStepResp, error)
		// GetUICaseStep 获取UI用例执行步骤
		GetUICaseStep(ctx context.Context, in *GetUICaseStepReq, opts ...grpc.CallOption) (*GetUICaseStepResp, error)
		// SearchUIDeviceRecord 搜索UI计划执行记录下的设备记录
		SearchUIDeviceRecord(ctx context.Context, in *SearchUIDeviceRecordReq, opts ...grpc.CallOption) (*SearchUIDeviceRecordResp, error)
		// SaveUIDevicePerfData 保存UI测试设备性能数据
		SaveUIDevicePerfData(ctx context.Context, in *SaveUIDevicePerfDataReq, opts ...grpc.CallOption) (*SaveUIDevicePerfDataResp, error)
		// GetUIDevicePerfData 获取UI测试设备性能数据
		GetUIDevicePerfData(ctx context.Context, in *GetUIDevicePerfDataReq, opts ...grpc.CallOption) (*GetUIDevicePerfDataResp, error)
	}

	defaultUIReporter struct {
		cli zrpc.Client
	}
)

func NewUIReporter(cli zrpc.Client) UIReporter {
	return &defaultUIReporter{
		cli: cli,
	}
}

// CreateUICaseRecord 创建UI用例执行记录
func (m *defaultUIReporter) CreateUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*CreateUICaseRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.CreateUICaseRecord(ctx, in, opts...)
}

// ModifyUICaseRecord 修改UI用例执行记录
func (m *defaultUIReporter) ModifyUICaseRecord(ctx context.Context, in *PutUICaseRecordRequest, opts ...grpc.CallOption) (*ModifyUICaseRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.ModifyUICaseRecord(ctx, in, opts...)
}

// CreateUISuiteRecord 创建UI集合执行记录
func (m *defaultUIReporter) CreateUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*CreateUISuiteRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.CreateUISuiteRecord(ctx, in, opts...)
}

// ModifyUISuiteRecord 修改UI集合执行记录
func (m *defaultUIReporter) ModifyUISuiteRecord(ctx context.Context, in *PutUISuiteRecordRequest, opts ...grpc.CallOption) (*ModifyUISuiteRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.ModifyUISuiteRecord(ctx, in, opts...)
}

// CreateUIPlanRecord 创建UI计划执行记录
func (m *defaultUIReporter) CreateUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*CreateUIPlanRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.CreateUIPlanRecord(ctx, in, opts...)
}

// ModifyUIPlanRecord 修改UI计划执行记录
func (m *defaultUIReporter) ModifyUIPlanRecord(ctx context.Context, in *PutUIPlanRecordRequest, opts ...grpc.CallOption) (*ModifyUIPlanRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.ModifyUIPlanRecord(ctx, in, opts...)
}

// ViewUIPlanRecord 查看UI计划执行记录
func (m *defaultUIReporter) ViewUIPlanRecord(ctx context.Context, in *ViewUIPlanRecordRequest, opts ...grpc.CallOption) (*ViewUIPlanRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.ViewUIPlanRecord(ctx, in, opts...)
}

// ListUIPlanRecord UI计划执行记录列表
func (m *defaultUIReporter) ListUIPlanRecord(ctx context.Context, in *ListUIPlanRecordRequest, opts ...grpc.CallOption) (*ListUIPlanRecordResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.ListUIPlanRecord(ctx, in, opts...)
}

// GetUIPlanCasesInfo 获取UI计划关联用例信息
func (m *defaultUIReporter) GetUIPlanCasesInfo(ctx context.Context, in *GetUIPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetUIPlanCasesInfoResponse, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.GetUIPlanCasesInfo(ctx, in, opts...)
}

// GetUIPlanRecord 获取UI计划执行记录
func (m *defaultUIReporter) GetUIPlanRecord(ctx context.Context, in *GetUIPlanRecordReq, opts ...grpc.CallOption) (*GetUIPlanRecordResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.GetUIPlanRecord(ctx, in, opts...)
}

// SearchUISuiteRecord 搜索UI计划执行记录下的UI集合执行记录
func (m *defaultUIReporter) SearchUISuiteRecord(ctx context.Context, in *SearchUISuiteRecordReq, opts ...grpc.CallOption) (*SearchUISuiteRecordResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.SearchUISuiteRecord(ctx, in, opts...)
}

// SearchUICaseRecord 搜索UI集合执行记录下的UI用例执行记录
func (m *defaultUIReporter) SearchUICaseRecord(ctx context.Context, in *SearchUICaseRecordReq, opts ...grpc.CallOption) (*SearchUICaseRecordResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.SearchUICaseRecord(ctx, in, opts...)
}

// GetUICaseRecord 获取UI用例执行记录
func (m *defaultUIReporter) GetUICaseRecord(ctx context.Context, in *GetUICaseRecordReq, opts ...grpc.CallOption) (*GetUICaseRecordResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.GetUICaseRecord(ctx, in, opts...)
}

// ListUICaseStep 获取UI用例执行步骤列表
func (m *defaultUIReporter) ListUICaseStep(ctx context.Context, in *ListUICaseStepReq, opts ...grpc.CallOption) (*ListUICaseStepResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.ListUICaseStep(ctx, in, opts...)
}

// GetUICaseStep 获取UI用例执行步骤
func (m *defaultUIReporter) GetUICaseStep(ctx context.Context, in *GetUICaseStepReq, opts ...grpc.CallOption) (*GetUICaseStepResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.GetUICaseStep(ctx, in, opts...)
}

// SearchUIDeviceRecord 搜索UI计划执行记录下的设备记录
func (m *defaultUIReporter) SearchUIDeviceRecord(ctx context.Context, in *SearchUIDeviceRecordReq, opts ...grpc.CallOption) (*SearchUIDeviceRecordResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.SearchUIDeviceRecord(ctx, in, opts...)
}

// SaveUIDevicePerfData 保存UI测试设备性能数据
func (m *defaultUIReporter) SaveUIDevicePerfData(ctx context.Context, in *SaveUIDevicePerfDataReq, opts ...grpc.CallOption) (*SaveUIDevicePerfDataResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.SaveUIDevicePerfData(ctx, in, opts...)
}

// GetUIDevicePerfData 获取UI测试设备性能数据
func (m *defaultUIReporter) GetUIDevicePerfData(ctx context.Context, in *GetUIDevicePerfDataReq, opts ...grpc.CallOption) (*GetUIDevicePerfDataResp, error) {
	client := pb.NewUIReporterClient(m.cli.Conn())
	return client.GetUIDevicePerfData(ctx, in, opts...)
}
