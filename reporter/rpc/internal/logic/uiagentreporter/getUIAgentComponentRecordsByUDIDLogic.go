package uiagentreporterlogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUIAgentComponentRecordsByUDIDLogic struct {
	*BaseLogic
}

func NewGetUIAgentComponentRecordsByUDIDLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *GetUIAgentComponentRecordsByUDIDLogic {
	return &GetUIAgentComponentRecordsByUDIDLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetUIAgentComponentRecordsByUDID 获取使用指定设备的`UI Agent`组件执行记录
func (l *GetUIAgentComponentRecordsByUDIDLogic) GetUIAgentComponentRecordsByUDID(in *pb.GetUIAgentComponentRecordsByUDIDReq) (
	out *pb.GetUIAgentComponentRecordsByUDIDResp, err error,
) {
	var (
		udid       = in.GetUdid()
		pagination = in.GetPagination()
	)

	return &pb.GetUIAgentComponentRecordsByUDIDResp{}, nil
}
