package dispatcherlogic

import (
	"context"
	"sync"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/mq"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var _ Publisher = (*UIAgentComponentPublisher)(nil)

type UIAgentComponentPublisher struct {
	*BasePublisher

	handleOnce    sync.Once
	componentData *managerpb.UIAgentComponentComponent
	executionData *managerpb.ApiExecutionData
	deviceData    *commonpb.UIAgentDevice
}

func NewUIAgentComponentPublisher(ctx context.Context, svcCtx *svc.ServiceContext) *UIAgentComponentPublisher {
	return &UIAgentComponentPublisher{
		BasePublisher: NewBasePublisher(ctx, svcCtx),
	}
}

func (p *UIAgentComponentPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	if err = p.handleMgrData(in); err != nil {
		return err
	}

	executeType := commonpb.ExecuteType_ET_EXECUTE
	if in.GetDebug() {
		// 调试
		executeType = commonpb.ExecuteType_ET_DEBUG
	} else if in.GetUiAgentComponent().GetReferenceId() != "" {
		// 参考执行
		executeType = commonpb.ExecuteType_ET_REFERENCE
	}

	if _, err = p.svcCtx.UIAgentReporterRPC.CreateUIAgentComponentRecord(
		p.ctx, &reporterpb.CreateUIAgentComponentRecordReq{
			TaskId:            p.taskId,
			ExecuteId:         p.executeId,
			ProjectId:         in.GetProjectId(),
			ComponentId:       in.GetUiAgentComponent().GetComponentId(),
			ComponentName:     p.componentData.GetName(),
			TriggerMode:       in.GetTriggerMode(),
			ExecuteType:       executeType,
			ApplicationConfig: p.componentData.GetApplicationConfig(),
			Mode:              p.componentData.GetMode(),
			Steps:             p.componentData.GetSteps(),
			Expectation:       p.componentData.GetExpectation(),
			Variables:         p.componentData.GetVariables(),
			Device:            p.deviceData,
			Reinstall:         in.GetUiAgentComponent().GetReinstall(),
			Restart:           in.GetUiAgentComponent().GetRestart(),
			ReferenceId:       in.GetUiAgentComponent().GetReferenceId(),
			Status:            pb.ComponentState_Pending.String(),
			ExecutedBy:        in.GetUserId(),
			StartedAt:         timestamppb.New(time.Now()),
		},
	); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.InternalError, err.Error()), "请求reporter rpc服务出现错误, error: %s", err,
		)
	}

	return nil
}

func (p *UIAgentComponentPublisher) Publish(in *pb.PublishReq) (out *pb.PublishResp, err error) {
	return p.publish(in)
}

func (p *UIAgentComponentPublisher) IsValid(_ *managerpb.ApiExecutionData) bool {
	// 调试不受固有状态影响
	return true
}

func (p *UIAgentComponentPublisher) Panic(in *pb.PublishReq, err error) {
	if err == nil {
		return
	}

	_, _ = p.svcCtx.UIAgentReporterRPC.ModifyUIAgentComponentRecord(
		p.ctx, &reporterpb.ModifyUIAgentComponentRecordReq{
			TaskId:      p.taskId,
			ExecuteId:   p.executeId,
			ProjectId:   in.GetProjectId(),
			ComponentId: in.GetUiAgentComponent().GetComponentId(),
			Status:      pb.ComponentState_Panic.String(),
			EndedAt:     timestamppb.New(time.Now()),
		},
	)

	if in.GetExecuteType() == managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT {
		return
	}

	// TODO: 发起回调
}

func (p *UIAgentComponentPublisher) handleMgrData(in *pb.PublishReq) (err error) {
	p.handleOnce.Do(
		func() {
			// 接口传入的`UI Agent`组件数据
			dstComponent := in.GetUiAgentComponent()
			if dstComponent == nil {
				err = errorx.Err(errorx.ValidateParamError, "`UI Agent`组件内容为空")
				return
			}

			p.executionData, err = p.getApiExecutionData(
				&managerpb.GetApiExecutionDataReq{
					ProjectId: in.GetProjectId(),
					Type:      managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT,
					Id:        dstComponent.GetComponentId(),
				},
			)
			if err != nil {
				return
			}

			// 从`manager`获取当前的`UI Agent`组件数据
			srcComponent := p.executionData.GetUiAgentComponent()
			if srcComponent == nil {
				err = errorx.Errorf(
					errorx.InternalError,
					"获取`UI Agent`组件执行数据失败, project_id: %s, component_id: %s",
					in.GetProjectId(), dstComponent.GetComponentId(),
				)
				return
			}

			if in.GetDebug() {
				// 调试时使用接口传入的数据
				srcComponent.Name = dstComponent.GetComponentName()
				srcComponent.ApplicationConfig = srcComponent.GetApplicationConfig()
				srcComponent.Mode = dstComponent.GetMode()
				srcComponent.Steps = dstComponent.GetSteps()
				srcComponent.Expectation = dstComponent.GetExpectation()
				srcComponent.Variables = dstComponent.GetVariables()

				// 调试时忽略参考配置
				dstComponent.ReferenceId = ""
			} else if dstComponent.GetMode() == commonpb.UIAgentMode_UIAgentMode_STEP {
				// Step模式时忽略参考配置
				dstComponent.ReferenceId = ""
			} else if dstComponent.GetMode() == commonpb.UIAgentMode_UIAgentMode_AGENT &&
				dstComponent.GetReferenceId() != "" &&
				dstComponent.GetReferenceId() != srcComponent.GetReferenceId() {
				// Agent模式 且 参考执行，需要校验传入的参考配置是否与绑定的一致
				err = errorx.Errorf(
					errorx.ProhibitedBehavior,
					"传入的参考配置跟`UI Agent`组件绑定的不一致, 期望的参考配置: %s, 但传入的参考配置: %s",
					srcComponent.GetReferenceId(), dstComponent.GetReferenceId(),
				)
				return
			}

			switch v := dstComponent.GetDevice().GetDevice().(type) {
			case *pb.UIAgentDeviceInfo_ProjectDevice:
				d := v.ProjectDevice

				var out *managerpb.GetProjectDeviceResp
				out, err = p.svcCtx.ManagerRPC.GetProjectDevice(
					p.ctx, &managerpb.GetProjectDeviceReq{
						ProjectId: in.GetProjectId(),
						Usage:     commonpb.DeviceUsage_UI_TESTING,
						Udid:      d.GetUdid(),
					},
				)
				if err != nil {
					return
				}

				pd := out.GetDevice().GetDevice()
				if pd.GetToken() != d.GetToken() {
					err = errorx.Errorf(
						errorx.ProhibitedBehavior,
						"传入的设备令牌跟实际的不一致, 无法使用该设备, udid: %s, token: %s",
						d.GetUdid(), d.GetToken(),
					)
					return
				}

				p.deviceData = &commonpb.UIAgentDevice{
					Device: &commonpb.UIAgentDevice_ProjectDevice_{
						ProjectDevice: &commonpb.UIAgentDevice_ProjectDevice{
							DeviceType:    pd.GetType(),
							PlatformType:  pd.GetPlatform(),
							Udid:          d.GetUdid(),
							RemoteAddress: pd.GetRemoteAddress(),
							Token:         d.GetToken(),
						},
					},
				}
			case *pb.UIAgentDeviceInfo_UserDevice:
				d := v.UserDevice

				// 对于`Android`设备，获取其真正的设备编号
				if d.GetPlatformType() == commonpb.PlatformType_ANDROID ||
					d.GetPlatformType() == commonpb.PlatformType_HarmonyOS {
					var out *managerpb.GetRemoteAndroidSerialResp
					out, err = p.svcCtx.ManagerRPC.GetRemoteAndroidSerial(
						p.ctx, &managerpb.GetRemoteAndroidSerialReq{
							DeviceType:    d.GetDeviceType(),
							RemoteAddress: d.GetRemoteAddress(),
						},
					)
					if err != nil {
						return
					}

					if serial := out.GetSerial(); serial != d.GetUdid() {
						p.Infof(
							"change the udid of android device, device_type: %s, platform_type: %s, remote_address: %s, udid: %s -> %s",
							d.GetDeviceType(), d.GetPlatformType(), d.GetRemoteAddress(), d.GetUdid(), serial,
						)
						d.Udid = serial
					}
				}

				// TODO: 通过设备编号查询该设备是否存在未结束的`UI Agent`任务

				p.deviceData = &commonpb.UIAgentDevice{
					Device: &commonpb.UIAgentDevice_UserDevice_{
						UserDevice: &commonpb.UIAgentDevice_UserDevice{
							DeviceType:    d.GetDeviceType(),
							PlatformType:  d.GetPlatformType(),
							Udid:          d.GetUdid(),
							RemoteAddress: d.GetRemoteAddress(),
						},
					},
				}
			default:
				err = errorx.Errorf(
					errorx.ValidateParamError,
					"无效的设备信息类型, 期望的类型: %T or %T, 但实际类型: %T",
					(*pb.UIAgentDeviceInfo_ProjectDevice)(nil), (*pb.UIAgentDeviceInfo_UserDevice)(nil), v,
				)
				return
			}

			if in.GetTriggerMode() == commonpb.TriggerMode_SCHEDULE {
				if srcComponent.GetMaintainedBy() != "" {
					in.UserId = srcComponent.GetMaintainedBy()
				} else {
					in.UserId = srcComponent.GetCreatedBy()
				}
			}

			p.componentData = srcComponent
			p.Infof("mgrData: %s", protobuf.MarshalJSONIgnoreError(p.executionData))
		},
	)

	return err
}

func (p *UIAgentComponentPublisher) publish(in *pb.PublishReq) (out *pb.PublishResp, err error) {
	if err = p.handleMgrData(in); err != nil {
		return nil, err
	}

	if !p.IsValid(p.executionData) || p.isStop {
		return &pb.PublishResp{TaskId: p.taskId, ExecuteId: p.executeId}, nil
	}

	taskData := p.getWorkerReq(in)
	payload, err := protobuf.MarshalJSON(taskData)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.SerializationError, err.Error()), "UI Agent任务参数序列化失败, error: %s", err,
		)
	}
	p.Infof("ui agent component task data: %s", payload)

	task := base.NewTask(
		constants.MQTaskTypeUIAgentWorkerExecuteComponentTask,
		payload,
		base.WithMaxRetryOptions(0),
		base.WithTimeoutOptions(time.Hour),
		base.WithRetentionOptions(time.Hour),
	)
	if _, err = p.svcCtx.UIAgentWorkerProducer.Send(
		p.ctx, task, mq.ConvertPbEnumerationToQueuePriority(taskData.GetPriorityType()),
	); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.InternalError, err.Error()), "发送数据到mq失败, error: %s", err)
	}

	return &pb.PublishResp{
		TaskId:    p.taskId,
		ExecuteId: p.executeId,
		Version:   "",
	}, nil
}

func (p *UIAgentComponentPublisher) getWorkerReq(in *pb.PublishReq) *pb.WorkerReq {
	component := in.GetUiAgentComponent()

	return &pb.WorkerReq{
		TriggerMode:  in.GetTriggerMode(),
		TriggerRule:  in.GetTriggerRule(),
		ProjectId:    in.GetProjectId(),
		TaskId:       p.taskId,
		ExecuteId:    p.executeId,
		ExecuteType:  in.GetExecuteType(),
		WorkerType:   pb.WorkerType_WorkerType_UI_AGENT_COMPONENT,
		UserId:       in.GetUserId(),
		NodeData:     p.executionData,
		PriorityType: p.priorityType,
		Data: &pb.WorkerReq_UiAgentComponent{
			UiAgentComponent: &pb.UIAgentComponentWorkerInfo{
				UiAgentComponentId:        component.GetComponentId(),
				UiAgentComponentExecuteId: p.executeId,
				Device:                    p.deviceData,
				Reinstall:                 component.GetReinstall(),
				Restart:                   component.GetRestart(),
				ReferenceId:               component.GetReferenceId(),
			},
		},
		Debug: in.GetDebug(),
	}
}
