package svc

import (
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/config"

	rediscommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
)

type ServiceContext struct {
	Config config.Config

	DB    sqlx.SqlConn
	Redis red.UniversalClient

	TaskInfoRecordModel model.TaskInfoRecordModel

	ManagerRPC           *zrpc.ManagerRPC
	ReporterRPC          *zrpc.ReporterRPC
	UIReporterRPC        *zrpc.UIReporterRPC
	PerfReporterRPC      *zrpc.PerfReporterRPC
	StabilityReporterRPC *zrpc.StabilityReporterRPC
	UIAgentReporterRPC   *zrpc.UIAgentReporterRPC
	UserRPC              *zrpc.UserRPC

	ApiWorkerProducer       *producer.Producer
	PerfWorkerProducer      *producer.Producer
	UIWorkerProducer        *producer.Producer
	StabilityWorkerProducer *producer.Producer
	UIAgentWorkerProducer   *producer.Producer
	DispatcherProducer      *producer.Producer
	TaskConsumer            *consumer.Consumer
	BeatConsumer            *consumer.Consumer
	WorkerProducer          *producer.Producer

	RCacheService     *rediscommon.RCacheService
	TaskInfoProcessor *task.InfoProcessor
}

func NewServiceContext(c config.Config) *ServiceContext {
	cacheService := rediscommon.NewRCacheService(c.RedisConf, rediscommon.DispatcherRedisKey)
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	return &ServiceContext{
		Config: c,

		Redis: redis.NewClient(c.RedisConf),

		TaskInfoRecordModel: model.NewTaskInfoRecordModel(sqlConn, c.Cache),

		ManagerRPC:           zrpc.NewManagerRPC(c.Manager),
		ReporterRPC:          zrpc.NewReporterRPC(c.Reporter),
		UIReporterRPC:        zrpc.NewUIReporterRPC(c.Reporter),
		PerfReporterRPC:      zrpc.NewPerfReporterRPC(c.Reporter),
		StabilityReporterRPC: zrpc.NewStabilityReporterRPC(c.Reporter),
		UIAgentReporterRPC:   zrpc.NewUIAgentReporterRPC(c.Reporter),
		UserRPC:              zrpc.NewUserRPC(c.User),

		ApiWorkerProducer:       producer.NewProducer(c.ApiWorkerProducer),
		UIWorkerProducer:        producer.NewProducer(c.UIWorkerProducer),
		PerfWorkerProducer:      producer.NewProducer(c.PerfWorkerProducer),
		StabilityWorkerProducer: producer.NewProducer(c.StabilityWorkerProducer),
		UIAgentWorkerProducer:   producer.NewProducer(c.UIAgentWorkerProducer),
		DispatcherProducer:      producer.NewProducer(c.DispatcherProducer),
		TaskConsumer:            consumer.NewConsumer(c.TaskConsumer),
		BeatConsumer:            consumer.NewConsumer(c.BeatConsumer),
		WorkerProducer:          producer.NewProducer(c.WorkerProducer),

		RCacheService:     cacheService,
		TaskInfoProcessor: task.NewTaskInfoProcessor(cacheService),
	}
}
