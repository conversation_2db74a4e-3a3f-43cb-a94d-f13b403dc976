package task

import (
	"context"
	"time"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PlanLogic struct {
	logx.Logger
	ctx         context.Context
	svcCtx      *svc.ServiceContext
	currentUser *userinfo.UserInfo
}

func NewPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PlanLogic {
	return &PlanLogic{
		currentUser: userinfo.FromContext(ctx),
		Logger:      logx.WithContext(ctx),
		ctx:         ctx,
		svcCtx:      svcCtx,
	}
}

func (l *PlanLogic) Plan(req *types.PlanReq) (resp *types.PlanResp, err error) {
	var (
		subEnvInfo  *pb.PublishReq_SubEnvInfo
		apiPlanInfo *pb.ApiPlanInfo
		uiPlanInfo  *pb.UiPlanInfo
	)
	if req.SubEnvInfo != nil {
		subEnvInfo = &pb.PublishReq_SubEnvInfo{
			SubEnvName: req.SubEnvInfo.SubEnvName,
		}
	}
	if len(req.Services) > 0 {
		apiPlanInfo = &pb.ApiPlanInfo{
			Services: req.Services,
		}
	}
	if req.UiPlanInfo != nil {
		uiPlanInfo = &pb.UiPlanInfo{
			AppDownloadUrl: req.UiPlanInfo.AppDownloadUrl,
			AppVersion:     req.UiPlanInfo.AppVersion,
			Devices:        req.UiPlanInfo.Devices,
			Together:       req.UiPlanInfo.Together,
		}
	}

	var triggerUser pb.PublishReq_TriggerUser
	if req.TriggerUser != nil {
		_ = copier.Copy(&triggerUser, &req.TriggerUser)
	}

	var approvers []*pb.PublishReq_ApproverUser
	if req.Approvers != nil {
		_ = copier.Copy(&approvers, &req.Approvers)
	}

	triggerMode := commonpb.TriggerMode(commonpb.TriggerMode_value[req.TriggerMode])
	planType := managerpb.ApiExecutionDataType(req.PlanType)
	in := &pb.PublishReq{
		TriggerMode: triggerMode,
		ProjectId:   req.ProjectId,
		ExecuteType: planType,
		UserId:      "",
		Debug:       req.Debug,
		SubEnvInfo:  subEnvInfo,
		TriggerUser: &triggerUser,
		Approvers:   approvers,
	}

	switch planType {
	case managerpb.ApiExecutionDataType_API_PLAN:
		in.PublishType = pb.PublishType_PublishType_API_PLAN
		in.Data = &pb.PublishReq_Plan{
			Plan: &pb.PlanPublishInfo{
				PlanId:          req.PlanId,
				CallbackUrl:     req.CallbackUrl,
				CallbackTimeout: req.CallbackTimeout,
				ApiPlanInfo:     apiPlanInfo,
			},
		}
	case managerpb.ApiExecutionDataType_UI_PLAN:
		in.PublishType = pb.PublishType_PublishType_UI_PLAN
		in.PriorityType = commonpb.PriorityType(req.PriorityType)
		in.Data = &pb.PublishReq_UiPlan{
			UiPlan: &pb.UIPlanPublishInfo{
				UiPlanId:        req.PlanId,
				CallbackUrl:     req.CallbackUrl,
				CallbackTimeout: req.CallbackTimeout,
				UiPlanInfo:      uiPlanInfo,
			},
		}
	case managerpb.ApiExecutionDataType_PERF_PLAN:
		if triggerMode == commonpb.TriggerMode_INTERFACE {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the perf plan cannot be triggered by api, project_id: %s, plan_type: %s, plan_id: %s, trigger_mode: %s",
				req.ProjectId, planType, req.PlanId, triggerMode,
			)
		}
		if req.PerfPlanInfo == nil {
			return nil, errorx.Errorf(
				errorx.ValidateParamError,
				"the perf plan info cannot be null, project_id: %s, plan_type: %s, plan_id: %s",
				req.ProjectId, planType, req.PlanId,
			)
		}

		in.PublishType = pb.PublishType_PublishType_PERF_PLAN
		in.PriorityType = commonpb.PriorityType(req.PriorityType)
		in.Data = &pb.PublishReq_PerfPlan{
			PerfPlan: &pb.PerfPlanPublishInfo{
				PerfPlanId: req.PlanId,
				PerfPlanInfo: &pb.PerfPlanInfo{
					ExecuteType:             commonpb.PerfTaskType(req.PerfPlanInfo.ExecuteType),
					EstimatedTime:           req.PerfPlanInfo.EstimatedTime,
					SendPreviewNotification: req.PerfPlanInfo.SendPreviewNotification,
				},
			},
		}

		if et := in.GetPerfPlan().GetPerfPlanInfo().GetEstimatedTime(); et != 0 {
			now := time.Now()
			startedAt := time.Unix(et, 0)
			if now.After(startedAt) {
				return nil, errorx.Errorf(
					errorx.ProhibitedBehavior,
					"the specified start time cannot be earlier than now, project_id: %s, plan_type: %s, plan_id: %s",
					req.ProjectId, planType, req.PlanId,
				)
			} else if startedAt.Sub(now) > 30*time.Minute {
				return nil, errorx.Errorf(
					errorx.ProhibitedBehavior,
					"the specified start time cannot be later than 30 minutes from now, project_id: %s, plan_type: %s, plan_id: %s",
					req.ProjectId, planType, req.PlanId,
				)
			}
		}
	case managerpb.ApiExecutionDataType_STABILITY_PLAN:
		in.PublishType = pb.PublishType_PublishType_STABILITY_PLAN
		in.PriorityType = commonpb.PriorityType(req.PriorityType)
		in.Data = &pb.PublishReq_StabilityPlan{
			StabilityPlan: &pb.StabilityPlanPublishInfo{
				StabilityPlanId: req.PlanId,
			},
		}
	default:
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior, "invalid plan type, project_id: %s, plan_type: %s, plan_id: %s",
			req.ProjectId, planType, req.PlanId,
		)
	}

	out, err := l.svcCtx.DispatcherRPC.Publish(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.PlanResp{TaskId: out.GetTaskId(), ExecuteId: out.GetExecuteId()}, nil
}
