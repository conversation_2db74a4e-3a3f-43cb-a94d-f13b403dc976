package task

import (
	"context"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type StopLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewStopLogic(ctx context.Context, svcCtx *svc.ServiceContext) *StopLogic {
	return &StopLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *StopLogic) Stop(req *types.StopReq) (resp *types.StopResp, err error) {
	_, err = l.svcCtx.DispatcherRPC.Stop(
		l.ctx, &pb.StopReq{
			ProjectId:   req.ProjectId,
			TaskId:      req.TaskId,
			Id:          req.Id,
			ExecuteType: managerpb.ApiExecutionDataType(req.Type),
			ExecuteId:   req.ExecuteId,

			Metadata: l.handleStopMetadata(req.Metadata),
		},
	)
	if err != nil {
		return nil, err
	}

	return &types.StopResp{}, nil
}

func (l *StopLogic) handleStopMetadata(in *types.StopMetadata) *pb.StopMetadata {
	out := &pb.StopMetadata{
		StopType: pb.StopType_StopType_Manual,
	}
	if in == nil {
		return out
	}

	if _, ok := pb.StopType_name[in.StopType]; ok && in.StopType != 0 {
		out.StopType = pb.StopType(in.StopType)
	}
	out.Reason = in.Reason

	if in.Detail == nil {
		return out
	}

	if in.Detail.Rule != nil {
		rule := &pb.StopDetailOfPerfStopRule{}
		if err := utils.Copy(rule, in.Detail.Rule); err != nil {
			l.Warnf(
				"failed to copy the stop detail, detail: %s, error: %+v", jsonx.MarshalIgnoreError(in.Detail.Rule), err,
			)
			return out
		}

		out.Detail = &pb.StopMetadata_Rule{
			Rule: rule,
		}
	}

	return out
}
