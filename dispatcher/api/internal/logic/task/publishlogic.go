package task

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type PublishLogic struct {
	logx.Logger
	ctx         context.Context
	svcCtx      *svc.ServiceContext
	currentUser *userinfo.UserInfo
}

func NewPublishLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PublishLogic {
	return &PublishLogic{
		Logger:      logx.WithContext(ctx),
		ctx:         ctx,
		svcCtx:      svcCtx,
		currentUser: userinfo.FromContext(ctx),
	}
}

func (l *PublishLogic) apiExecutionDataType2PublishType(typ managerpb.ApiExecutionDataType) pb.PublishType {
	m := map[managerpb.ApiExecutionDataType]pb.PublishType{
		managerpb.ApiExecutionDataType_API_COMPONENT_GROUP: pb.PublishType_PublishType_API_COMPONENT_GROUP,
		managerpb.ApiExecutionDataType_API_CASE:            pb.PublishType_PublishType_API_CASE,
		managerpb.ApiExecutionDataType_API_SUITE:           pb.PublishType_PublishType_API_SUITE,
		managerpb.ApiExecutionDataType_API_PLAN:            pb.PublishType_PublishType_API_PLAN,
		managerpb.ApiExecutionDataType_INTERFACE_CASE:      pb.PublishType_PublishType_INTERFACE_CASE,
		managerpb.ApiExecutionDataType_INTERFACE_DOCUMENT:  pb.PublishType_PublishType_INTERFACE_DOCUMENT,
		managerpb.ApiExecutionDataType_UI_CASE:             pb.PublishType_PublishType_UI_CASE,
		managerpb.ApiExecutionDataType_UI_SUITE:            pb.PublishType_PublishType_UI_SUITE,
		managerpb.ApiExecutionDataType_UI_PLAN:             pb.PublishType_PublishType_UI_PLAN,
		managerpb.ApiExecutionDataType_PERF_PLAN:           pb.PublishType_PublishType_PERF_PLAN,
		managerpb.ApiExecutionDataType_STABILITY_PLAN:      pb.PublishType_PublishType_STABILITY_PLAN,
		managerpb.ApiExecutionDataType_UI_AGENT_COMPONENT:  pb.PublishType_PublishType_UI_AGENT_COMPONENT,
		managerpb.ApiExecutionDataType_UI_AGENT_CASE:       pb.PublishType_PublishType_UI_AGENT_CASE,
		managerpb.ApiExecutionDataType_UI_AGENT_SUITE:      pb.PublishType_PublishType_UI_AGENT_SUITE,
		managerpb.ApiExecutionDataType_UI_AGENT_PLAN:       pb.PublishType_PublishType_UI_AGENT_PLAN,
	}
	return m[typ]
}

func (l *PublishLogic) Publish(in *types.PublishReq) (resp *types.PublishResp, err error) {
	req := &pb.PublishReq{
		TriggerMode: commonpb.TriggerMode_MANUAL,
		ProjectId:   in.ProjectId,
		ExecuteType: managerpb.ApiExecutionDataType(in.Type),
		PublishType: l.apiExecutionDataType2PublishType(managerpb.ApiExecutionDataType(in.Type)),
		UserId:      l.currentUser.Account,
		Debug:       in.Debug,
	}

	err = l.genPublishData(req.GetPublishType(), in, req)
	if err != nil {
		return nil, err
	}

	res, err := l.svcCtx.DispatcherRPC.Publish(l.ctx, req)
	if err != nil {
		return nil, err
	}

	return &types.PublishResp{TaskId: res.GetTaskId(), ExecuteId: res.GetExecuteId(), Version: res.GetVersion()}, nil
}

func (l *PublishLogic) genPublishData(typ pb.PublishType, in *types.PublishReq, req *pb.PublishReq) error {
	switch typ {
	case pb.PublishType_PublishType_API_COMPONENT_GROUP:
		req.Data = &pb.PublishReq_ComponentGroup{
			ComponentGroup: &pb.ComponentGroupPublishInfo{
				ComponentGroupId: in.Id,
				GeneralConfig:    l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig:    l.genAccountConfig(in.AccountConfig),
				Version:          in.Version,
			},
		}
	case pb.PublishType_PublishType_API_CASE:
		req.Data = &pb.PublishReq_Case{
			Case: &pb.CasePublishInfo{
				CaseId:        in.Id,
				GeneralConfig: l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig: l.genAccountConfig(in.AccountConfig),
				Version:       in.Version,
			},
		}
	case pb.PublishType_PublishType_API_SUITE:
		req.Data = &pb.PublishReq_Suite{
			Suite: &pb.SuitePublishInfo{
				SuiteId:       in.Id,
				GeneralConfig: l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig: l.genAccountConfig(in.AccountConfig),
				// CaseExecutionMode:
			},
		}
	case pb.PublishType_PublishType_API_PLAN:
		req.Data = &pb.PublishReq_Plan{
			Plan: &pb.PlanPublishInfo{
				PlanId: in.Id,
			},
		}
	case pb.PublishType_PublishType_INTERFACE_DOCUMENT:
		cases, err := l.genInterfaceCases(in)
		if err != nil {
			return err
		}

		req.Data = &pb.PublishReq_Interface{
			Interface: &pb.InterfaceDocumentPublishInfo{
				InterfaceDocumentId: in.Id,
				GeneralConfig:       l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig:       l.genAccountConfig(in.AccountConfig),
				InterfaceCases:      cases,
			},
		}
	case pb.PublishType_PublishType_INTERFACE_CASE:
		req.Data = &pb.PublishReq_InterfaceCase{
			InterfaceCase: &pb.InterfaceCasePublishInfo{
				InterfaceCaseId: in.Id,
				GeneralConfig:   l.genGeneralConfig(&in.GeneralConfig),
				AccountConfig:   l.genAccountConfig(in.AccountConfig),
				Version:         in.Version,
			},
		}
	case pb.PublishType_PublishType_UI_CASE:
		req.Data = &pb.PublishReq_UiCase{
			UiCase: &pb.UICasePublishInfo{
				UiCaseId: in.Id,
			},
		}
	case pb.PublishType_PublishType_UI_SUITE:
		req.Data = &pb.PublishReq_UiSuite{
			UiSuite: &pb.UISuitePublishInfo{
				UiSuiteId: in.Id,
			},
		}
	case pb.PublishType_PublishType_UI_PLAN:
		req.Data = &pb.PublishReq_UiPlan{
			UiPlan: &pb.UIPlanPublishInfo{
				UiPlanId: in.Id,
			},
		}
	case pb.PublishType_PublishType_PERF_PLAN:
		req.Data = &pb.PublishReq_PerfPlan{
			PerfPlan: &pb.PerfPlanPublishInfo{
				PerfPlanId: in.Id,
				PerfPlanInfo: &pb.PerfPlanInfo{
					ExecuteType:             commonpb.PerfTaskType_DEBUG,
					EstimatedTime:           0,
					SendPreviewNotification: false,
				},
			},
		}
	case pb.PublishType_PublishType_STABILITY_PLAN:
		req.Data = &pb.PublishReq_StabilityPlan{
			StabilityPlan: &pb.StabilityPlanPublishInfo{
				StabilityPlanId: in.Id,
			},
		}
	case pb.PublishType_PublishType_UI_AGENT_COMPONENT:
		component := in.UIAgentComponent
		if component == nil {
			return errorx.Err(errorx.ValidateParamError, "UI Agent组件内容为空")
		}

		mode := l.getUIAgentMode(component.Mode)
		steps := component.AgentModeSteps
		if mode == commonpb.UIAgentMode_UIAgentMode_STEP {
			steps = component.StepModeSteps
		}

		req.Data = &pb.PublishReq_UiAgentComponent{
			UiAgentComponent: &pb.UIAgentComponentPublishInfo{
				ComponentId:   in.Id,
				ComponentName: component.Name,
				ApplicationId: component.ApplicationId,
				Mode:          mode,
				Steps:         l.genUIAgentComponentSteps(steps),
				Expectation:   l.genUIAgentComponentExpectation(component.Expectation),
				Variables:     l.genVariables(component.Variables),
				Device:        l.genUIAgentDevice(component.Device),
				Reinstall:     component.Reinstall,
				Restart:       component.Restart,
				ReferenceId:   component.ReferenceId,
			},
		}
	}

	return nil
}

func (l *PublishLogic) genInterfaceCases(in *types.PublishReq) ([]*managerpb.ApiExecutionData, error) {
	if len(in.InterfaceCaseIds) == 0 {
		return nil, nil
	}

	cases := make([]*managerpb.ApiExecutionData, len(in.InterfaceCaseIds))
	for idx, item := range in.InterfaceCaseIds {
		apiExecutionData, err := l.getApiExecutionData(
			in.ProjectId, managerpb.ApiExecutionDataType(item.Type), item.Id, item.Version,
		)
		if err != nil {
			return nil, err
		}
		cases[idx] = apiExecutionData
	}
	return cases, nil
}

func (l *PublishLogic) getApiExecutionData(
	productId string, typ managerpb.ApiExecutionDataType, id, version string,
) (resp *managerpb.ApiExecutionData, err error) {
	resp, err = l.svcCtx.ManagerRPC.GetApiExecutionData(
		l.ctx,
		&managerpb.GetApiExecutionDataReq{
			ProjectId: productId,
			Id:        id,
			Type:      typ,
			Version:   version,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(err, "获取执行数据失败, error: %s", err)
	}

	return resp, nil
}

func (l *PublishLogic) genGeneralConfig(in *commontypes.ApiGeneralConfig) *commonpb.GeneralConfig {
	config := &commonpb.GeneralConfig{
		ProjectId:   in.ProjectId,
		ConfigId:    in.ConfigId,
		Name:        in.Name,
		Description: in.Description,
		BaseUrl:     in.BaseUrl,
		Verify:      in.Verify,
		Variables:   make([]*commonpb.GeneralConfigVar, len(in.Variables)),
	}

	for idx, kv := range in.Variables {
		config.Variables[idx] = &commonpb.GeneralConfigVar{
			Key:   kv.Key,
			Value: kv.Value,
		}
	}

	return config
}

func (l *PublishLogic) genAccountConfig(in []commontypes.ApiAccountConfig) []*commonpb.AccountConfig {
	configs := make([]*commonpb.AccountConfig, len(in))
	for idx, kv := range in {
		configs[idx] = &commonpb.AccountConfig{
			ProjectId:    kv.ProjectId,
			ConfigId:     kv.ConfigId,
			Name:         kv.Name,
			Description:  kv.Description,
			ProductType:  kv.ProductType,
			ProductName:  kv.ProductName,
			PoolEnvTable: kv.PoolEnvTable,
			PoolEnvName:  kv.PoolEnvName,
		}
	}
	return configs
}

func (l *PublishLogic) genInterfaceCaseIds(in *types.PublishReq) []*pb.ComponentExecuteKey {
	// 接口用例特殊逻辑
	if managerpb.ApiExecutionDataType(in.Type) != managerpb.ApiExecutionDataType_INTERFACE_CASE {
		return nil
	}

	keys := make([]*pb.ComponentExecuteKey, len(in.InterfaceCaseIds))
	for idx, item := range in.InterfaceCaseIds {
		keys[idx] = &pb.ComponentExecuteKey{
			Key: &pb.ComponentKey{
				ComponentId:   item.Id,
				ComponentType: managerpb.ApiExecutionDataType(item.Type),
				Version:       item.Version,
			},
		}
	}
	return keys
}

func (l *PublishLogic) genApplicationConfig(projectID, configID string) (*commonpb.ApplicationConfig, error) {
	resp, err := l.svcCtx.ManagerRPC.ViewApplicationConfiguration(
		l.ctx, &managerpb.ViewApplicationConfigurationReq{
			ProjectId: projectID,
			ConfigId:  configID,
		},
	)
	if err != nil {
		return nil, errors.Wrapf(err, "获取应用配置失败, error: %s", err)
	}

	c := resp.GetConfiguration()
	config := &commonpb.ApplicationConfig{
		ProjectId:       projectID,
		ConfigId:        configID,
		Name:            c.GetName(),
		Description:     c.GetDescription(),
		PlatformType:    c.GetPlatformType(),
		AppId:           c.GetAppId(),
		AppDownloadLink: c.GetAppDownloadLink(),
		Prompts:         make([]*commonpb.PromptConfig, 0, len(c.GetPrompts())),
	}
	for _, item := range c.GetPrompts() {
		config.Prompts = append(
			config.Prompts, &commonpb.PromptConfig{
				ProjectId:   item.GetProjectId(),
				ConfigId:    item.GetConfigId(),
				Purpose:     item.GetPurpose(),
				Category:    item.GetCategory(),
				Name:        item.GetName(),
				Description: item.GetDescription(),
				Content:     item.GetContent(),
			},
		)
	}

	return config, nil
}

func (l *PublishLogic) getUIAgentMode(mode int8) commonpb.UIAgentMode {
	if mode == int8(commonpb.UIAgentMode_UIAgentMode_STEP) {
		return commonpb.UIAgentMode_UIAgentMode_STEP
	}

	return commonpb.UIAgentMode_UIAgentMode_AGENT
}

func (l *PublishLogic) genUIAgentComponentSteps(in []*commontypes.UIAgentComponentStep) []*commonpb.UIAgentComponentStep {
	steps := make([]*commonpb.UIAgentComponentStep, 0, len(in))
	for _, item := range in {
		if item == nil {
			continue
		}

		steps = append(
			steps, &commonpb.UIAgentComponentStep{
				Content:     item.Content,
				Expectation: l.genUIAgentComponentExpectation(item.Expectation),
			},
		)
	}

	return steps
}

func (l *PublishLogic) genUIAgentComponentExpectation(in *commontypes.UIAgentComponentExpectation) *commonpb.UIAgentComponentExpectation {
	if in == nil {
		return nil
	}

	return &commonpb.UIAgentComponentExpectation{
		Text:  in.Text,
		Image: in.Image,
	}
}

func (l *PublishLogic) genVariables(in []*commontypes.KeyValuePair) []*commonpb.GeneralConfigVar {
	variables := make([]*commonpb.GeneralConfigVar, 0, len(in))
	for _, item := range in {
		if item == nil {
			continue
		}

		variables = append(
			variables, &commonpb.GeneralConfigVar{
				Key:   item.Key,
				Value: item.Value,
			},
		)
	}

	return variables
}

func (l *PublishLogic) genUIAgentDevice(in *types.UIAgentDeviceInfo) *pb.UIAgentDeviceInfo {
	if in == nil {
		return nil
	}

	if d := in.ProjectDevice; d != nil {
		return &pb.UIAgentDeviceInfo{
			Device: &pb.UIAgentDeviceInfo_ProjectDevice{
				ProjectDevice: &pb.UIAgentDeviceInfo_ProjectDeviceInfo{
					Udid:  d.UDID,
					Token: d.Token,
				},
			},
		}
	}
	if d := in.UserDevice; d != nil {
		return &pb.UIAgentDeviceInfo{
			Device: &pb.UIAgentDeviceInfo_UserDevice{
				UserDevice: &pb.UIAgentDeviceInfo_UserDeviceInfo{
					DeviceType:    commonpb.DeviceType(d.DeviceType),
					PlatformType:  commonpb.PlatformType(d.PlatformType),
					Udid:          d.UDID,
					RemoteAddress: d.RemoteAddress,
				},
			},
		}
	}

	return nil
}
