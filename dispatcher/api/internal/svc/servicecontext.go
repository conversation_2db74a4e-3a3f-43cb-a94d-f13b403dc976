package svc

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
)

type ServiceContext struct {
	Config config.Config

	Validator *utils.Validator

	DispatcherRPC *zrpc.DispatcherRpc
	ManagerRPC    *zrpc.ManagerRPC
	UserRPC       *zrpc.UserRPC
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,

		Validator: utils.NewValidator(c.Validator.Locale),

		DispatcherRPC: zrpc.NewDispatcherRpc(c.Dispatcher),
		ManagerRPC:    zrpc.NewManagerRPC(c.Manager),
		UserRPC:       zrpc.NewUserRPC(c.User),
	}
}
