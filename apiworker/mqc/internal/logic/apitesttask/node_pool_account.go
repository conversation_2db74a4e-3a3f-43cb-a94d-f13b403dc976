package apitesttask

import (
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/protobuf/proto"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/client/account"
	accountpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"

	sqlbuilderPb "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
)

type PoolAccountNode struct {
	*BaseNode

	source *managerpb.PoolAccountComponent
	logW   *PoolAccountNodeLogWriter

	Env      *commonpb.AccountConfig
	Accounts []*accountpb.ReleaseTestAccountRequest_Account
}

func NewAccountNode(task *ApitestTask, data *managerpb.ApiExecutionData, parent Node, isShell bool) (Node, error) {
	node := &PoolAccountNode{}
	var err error
	node.BaseNode, err = NewBaseNode(task, data, node, parent, isShell)
	if err != nil {
		return nil, node.UpdateInvalidStatef(codes.NodeInvalid, "%s", err)
	}

	node.source = data.GetAccount()
	node.logW = NewPoolAccountNodeLogWriter(node.BaseNode.logW)
	node.Accounts = make([]*accountpb.ReleaseTestAccountRequest_Account, 0, 10)

	env, ok := task.AccountEnv(node.source.GetProductType())
	if !ok {
		return node, node.UpdateInvalidStatef(codes.NodeAccountNotEnv, "请检查账号池配置")
	}
	node.Env = env
	node.logW.SetPoolEnvTable(node.Env.GetPoolEnvTable())

	return node, nil
}

func (node *PoolAccountNode) run() (err error) {
	resp := &account.QueryAccountPoolEnvDataResponse{}
	err = node.RpcExec(
		func() (string, proto.Message, error) {
			rpcClient := node.task.svcCtx.AccountRpc
			logx.Infof("apiWorker调用account筛查账号, 当前时间戳是：%d", time.Now().UnixNano())
			resp, err = rpcClient.QueryEnv(node.task.ctx, node.getAccountPoolRequest())
			if err != nil {
				return rpcClient.Name, nil, err
			}
			return rpcClient.Name, resp, nil
		},
	)
	if err != nil {
		return node.UpdateFailureStatef(codes.NodePoolAccountQueryEnvFailure, "err: %s", err)
	}

	for _, match := range resp.GetMatchData() {
		if len(match.GetAccount()) == 0 {
			return node.UpdateFailureStatef(codes.NodePoolAccountNotFound, "找不到适合的账号")
		}

		logx.Infof(
			"apiWorker调用account拿到账号了: %s, 当前时间戳是：%d", match.GetAccount()[0].GetValue(),
			time.Now().UnixNano(),
		)

		for _, acc := range match.GetAccount() {
			val, ok := node.ColumnValue2GoValue(acc.GetColumnType(), acc.GetValue())
			if ok {
				node.task.VarsPool.SetNodeExportVar(node.Id(), acc.GetField(), val)
				node.logW.SetExport(acc.GetField(), val, errorx.OK, GetErrCode(errorx.OK).String(), "")
			}

			// 记录账号
			if acc.GetField() == "account" {
				node.Accounts = append(
					node.Accounts, &accountpb.ReleaseTestAccountRequest_Account{
						Account:   acc.GetValue(),
						LockValue: acc.GetLockValue(),
					},
				)
				node.logW.SetAccount(acc.GetValue())
			}
		}
	}

	node.task.RegisterTeardownFunc(
		func() {
			// 注册释放账号池
			_, err := node.task.svcCtx.AccountRpc.ReleaseTestAccount(
				node.task.ctx, &account.ReleaseTestAccountRequest{
					ReleaseTasAccountArray: []*accountpb.ReleaseTestAccountRequest_PoolAccount{
						{
							PoolEnvTable: node.Env.GetPoolEnvTable(),
							AccountArray: node.Accounts,
						},
					},
				},
			)
			if err != nil {
				_ = node.UpdateFailureStatef(codes.NodePoolAccountReleaseFailure, "err: %s", err)
			}
		},
	)

	return nil
}

func (node *PoolAccountNode) getAccountPoolRequest() *accountpb.QueryAccountPoolEnvDataRequest {
	condition := &sqlbuilderPb.Condition{}

	err := utils.Copy(
		condition, node.source.GetCondition(), utils.StringToPBEnum(managerpb.Relationship_AND),
		utils.PBEnumToString(managerpb.Relationship_AND),
	)
	if err != nil {
		logx.Errorf("生产accountPb.QueryAccountPoolEnvDataRequest对象发生错误：%s", err)
		return nil
	}

	req := &accountpb.QueryAccountPoolEnvDataRequest{
		PoolEnvTable:  node.Env.GetPoolEnvTable(),
		Condition:     condition,
		ExpectedCount: 1,
	}

	if len(node.source.GetExports()) > 0 {
		req.SelectedColumnIdArray = make([]string, 0, len(node.source.GetExports()))
		for _, pair := range node.source.GetExports() {
			req.SelectedColumnIdArray = append(req.SelectedColumnIdArray, pair.GetKey())
		}
	}

	return req
}

func (node *PoolAccountNode) ColumnValue2GoValue(typ accountpb.ColumnType, val string) (any, bool) {
	switch typ {
	case accountpb.ColumnType_VARCHAR:
		return val, true
	case accountpb.ColumnType_INT, accountpb.ColumnType_TINYINT:
		ival, err := strconv.Atoi(val)
		if err != nil {
			node.UpdateWarningStatef(codes.NodePoolAccountInvalidType, "atoi fail, %s, err: %s", val, err)
			return nil, false
		}
		return ival, true
	case accountpb.ColumnType_FLOAT:
		fval, err := strconv.ParseFloat(val, 64)
		if err != nil {
			node.UpdateWarningStatef(codes.NodePoolAccountInvalidType, "parseFloat fail, %s, err: %s", val, err)
			return nil, false
		}
		return fval, true
	case accountpb.ColumnType_DATETIME:
		return val, true
	case accountpb.ColumnType_TIMESTAMP:
		ival, err := strconv.ParseUint(val, 10, 64)
		if err != nil {
			node.UpdateWarningStatef(codes.NodePoolAccountInvalidType, "parseUint fail, %s, err: %s", val, err)
			return nil, false
		}
		return ival, true
	}
	return nil, false
}

func (node *PoolAccountNode) Logger() NodeLogger {
	return node.logW
}

func (node *PoolAccountNode) Content() string {
	return node.logW.toJson()
}
